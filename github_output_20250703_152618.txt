health_check_passed=true
SLACK_MESSAGE<<EOF
*Cypress Cucumber Test Report - Build 999*
*Status:* SUCCESS
*Branch:* `feature/health-check-test`
*Latest Commit:* `d4224e7`
*Workflow Link:* <https://github.com/investigate-app/test-repo/actions/runs/888888|View Workflow>

*Summary Per Feature:*
```
+------------------------------------------+
| Feature      Total Passed Failed Skipped |
+------------------------------------------+
| health-check 3     3      -      -       |
| Total        3     3      -      -       |
+------------------------------------------+
```

All scenarios passed or were skipped!
EOF
SLACK_MESSAGE_STATUS=SUCCESS
