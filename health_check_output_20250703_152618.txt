

DevTools listening on ws://127.0.0.1:60361/devtools/browser/b36a60b2-d9bc-4e84-ae42-6fb5295b5587
(node:41460) ExperimentalWarning: `--experimental-loader` may be removed in the future; instead use `register()`:
--import 'data:text/javascript,import { register } from "node:module"; import { pathToFileURL } from "node:url"; register("file%3A///D%3A/StoreData/Cypress/Cache/14.0.3/Cypress/resources/app/node_modules/ts-node/esm/transpile-only.mjs", pathToFileURL("./"));'
(Use `node --trace-warnings ...` to show where the warning was created)
(node:41460) [DEP0180] DeprecationWarning: fs.Stats constructor is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)

====================================================================================================

  (Run Starting)

  ┌────────────────────────────────────────────────────────────────────────────────────────────────┐
  │ Cypress:        14.0.3                                                                         │
  │ Browser:        Chrome 137 (headless)                                                          │
  │ Node Version:   v24.1.0 (C:\nvm4w\nodejs\node.exe)                                             │
  │ Specs:          1 found (health-check.feature)                                                 │
  │ Searched:       C:\Projects\investigate-app\client\cypress\e2e\health-check\health-check.featu │
  │                 re                                                                             │
  └────────────────────────────────────────────────────────────────────────────────────────────────┘


────────────────────────────────────────────────────────────────────────────────────────────────────
                                                                                                    
  Running:  health-check.feature                                                            (1 of 1)


  Health Check
    √ Health Check Case Management screen (27746ms)
    √ Health Check Settings screen (23244ms)
    √ Health Check Search screen (19342ms)


  3 passing (1m)


  (Results)

  ┌────────────────────────────────────────────────────────────────────────────────────────────────┐
  │ Tests:        3                                                                                │
  │ Passing:      3                                                                                │
  │ Failing:      0                                                                                │
  │ Pending:      0                                                                                │
  │ Skipped:      0                                                                                │
  │ Screenshots:  0                                                                                │
  │ Video:        false                                                                            │
  │ Duration:     1 minute, 10 seconds                                                             │
  │ Spec Ran:     health-check.feature                                                             │
  └────────────────────────────────────────────────────────────────────────────────────────────────┘


====================================================================================================

  (Run Finished)


       Spec                                              Tests  Passing  Failing  Pending  Skipped  
  ┌────────────────────────────────────────────────────────────────────────────────────────────────┐
  │ ✔  health-check.feature                     01:10        3        3        -        -        - │
  └────────────────────────────────────────────────────────────────────────────────────────────────┘
    ✔  All specs passed!                        01:10        3        3        -        -        -  

