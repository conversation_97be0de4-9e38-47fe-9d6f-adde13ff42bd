SLACK_MESSAGE<<EOF
*Cypress Cucumber Test Report - Build 123*
*Status:* SUCCESS
*Branch:* `feature/test-branch`
*Latest Commit:* `abc123d`
*Workflow Link:* <https://github.com/investigate-app/test-repo/actions/runs/999999|View Workflow>

*Summary Per Feature:*
```
+--------------------------------------------+
| Feature        Total Passed Failed Skipped |
+--------------------------------------------+
| search-results 4     1      -      3       |
| Total          4     1      -      3       |
+--------------------------------------------+
```

All scenarios passed or were skipped!
EOF
SLACK_MESSAGE_STATUS=SUCCESS
SLACK_MESSAGE<<EOF
*Cypress Cucumber Test Report - Build 123*
*Status:* SUCCESS
*Branch:* `feature/test-branch`
*Latest Commit:* `abc123d`
*Workflow Link:* <https://github.com/investigate-app/test-repo/actions/runs/999999|View Workflow>

*Summary Per Feature:*
```
+--------------------------------------------+
| Feature        Total Passed Failed Skipped |
+--------------------------------------------+
| search-results 4     1      -      3       |
| Total          4     1      -      3       |
+--------------------------------------------+
```

All scenarios passed or were skipped!
EOF
SLACK_MESSAGE_STATUS=SUCCESS
