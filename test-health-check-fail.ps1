#!/usr/bin/env pwsh
# Script to test health check FAIL scenario

Write-Host "============================================" -ForegroundColor Red
Write-Host "HEALTH CHECK TEST - FAIL SCENARIO" -ForegroundColor Red
Write-Host "============================================" -ForegroundColor Red
Write-Host ""

# Backup original cucumber.json
Write-Host "Backing up original cucumber.json..." -ForegroundColor Yellow
Copy-Item "client/cypress/reports/cucumber.json" "client/cypress/reports/cucumber_backup.json" -Force

# Modify cucumber.json to create failed tests
Write-Host "Modifying cucumber.json to simulate failed tests..." -ForegroundColor Yellow

$cucumberContent = Get-Content "client/cypress/reports/cucumber.json" -Raw

# Replace some "passed" statuses with "failed" to simulate failures
$cucumberContent = $cucumberContent -replace '"status": "passed",\s*"duration": 16447000000', '"status": "failed","duration": 16447000000,"error_message": "AssertionError: Search screen not loaded properly"'
$cucumberContent = $cucumberContent -replace '"status": "passed",\s*"duration": 974000000', '"status": "failed","duration": 974000000,"error_message": "TypeError: Cannot enter keyword in search field"'

# Save the modified content
$cucumberContent | Set-Content "client/cypress/reports/cucumber.json" -NoNewline

Write-Host "Running parse-report.sh script with failed tests..." -ForegroundColor Yellow

$env:GITHUB_OUTPUT = "output_fail.txt"
$env:GITHUB_RUN_NUMBER = "456"
$env:GITHUB_REF_NAME = "feature/test-branch"
$env:GITHUB_SHA = "def789abc123456"
$env:GITHUB_REPOSITORY = "investigate-app/test-repo"
$env:GITHUB_SERVER_URL = "https://github.com"
$env:GITHUB_RUN_ID = "888888"

# Execute the script using Git Bash
& "C:\Program Files\Git\bin\bash.exe" -c "
export GITHUB_OUTPUT='$env:GITHUB_OUTPUT'
export GITHUB_RUN_NUMBER='$env:GITHUB_RUN_NUMBER'
export GITHUB_REF_NAME='$env:GITHUB_REF_NAME'
export GITHUB_SHA='$env:GITHUB_SHA'
export GITHUB_REPOSITORY='$env:GITHUB_REPOSITORY'
export GITHUB_SERVER_URL='$env:GITHUB_SERVER_URL'
export GITHUB_RUN_ID='$env:GITHUB_RUN_ID'

bash .github/scripts/parse-report.sh
"

Write-Host ""
Write-Host "============================================" -ForegroundColor Red
Write-Host "RESULTS:" -ForegroundColor Red
Write-Host "============================================" -ForegroundColor Red

# Check the status
$status = Get-Content "output_fail.txt" | Select-String "SLACK_MESSAGE_STATUS" | Select-Object -Last 1
Write-Host "Status: $status" -ForegroundColor $(if ($status -match "FAILURE") { "Red" } else { "Green" })

Write-Host ""
Write-Host "Full Output:" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan
Get-Content "output_fail.txt"

# Restore original cucumber.json
Write-Host ""
Write-Host "Restoring original cucumber.json..." -ForegroundColor Yellow
Copy-Item "client/cypress/reports/cucumber_backup.json" "client/cypress/reports/cucumber.json" -Force
Remove-Item "client/cypress/reports/cucumber_backup.json" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "Test completed! Check output_fail.txt for full details." -ForegroundColor Red
Write-Host "Original cucumber.json has been restored." -ForegroundColor Green
