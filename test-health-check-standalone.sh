#!/bin/bash
# Standalone Health Check Test Script
# Replicates the GitHub workflow health check logic (lines 92-112 and 138-152 in daily_e2e.yml)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
HEALTH_CHECK_SPEC="cypress/e2e/health-check/health-check.feature"
WORKING_DIR="client"
BROWSER="chrome"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
HEALTH_CHECK_OUTPUT="health_check_output_${TIMESTAMP}.txt"
CUCUMBER_REPORT="client/cypress/reports/cucumber.json"

# GitHub Actions environment simulation
export GITHUB_RUN_NUMBER=${GITHUB_RUN_NUMBER:-"999"}
export GITHUB_REF_NAME=${GITHUB_REF_NAME:-"feature/health-check-test"}
export GITHUB_SHA=${GITHUB_SHA:-"$(git rev-parse HEAD 2>/dev/null || echo 'abc123def456789')"}
export GITHUB_REPOSITORY=${GITHUB_REPOSITORY:-"investigate-app/test-repo"}
export GITHUB_SERVER_URL=${GITHUB_SERVER_URL:-"https://github.com"}
export GITHUB_RUN_ID=${GITHUB_RUN_ID:-"888888"}
export GITHUB_OUTPUT="github_output_${TIMESTAMP}.txt"

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}🏥 STANDALONE HEALTH CHECK TEST${NC}"
echo -e "${BLUE}============================================${NC}"
echo ""

echo -e "${CYAN}📋 Test Configuration:${NC}"
echo "  • Health Check Spec: ${HEALTH_CHECK_SPEC}"
echo "  • Working Directory: ${WORKING_DIR}"
echo "  • Browser: ${BROWSER}"
echo "  • Build Number: ${GITHUB_RUN_NUMBER}"
echo "  • Branch: ${GITHUB_REF_NAME}"
echo "  • Commit SHA: ${GITHUB_SHA:0:7}"
echo "  • Repository: ${GITHUB_REPOSITORY}"
echo ""

# Step 1: Run Health Check (replicating lines 92-101)
echo -e "${YELLOW}🚀 Step 1: Running Health Check...${NC}"
echo "Command: cd ${WORKING_DIR} && yarn cy:runLocal --spec \"${HEALTH_CHECK_SPEC}\" --browser ${BROWSER}"
echo ""

cd "${WORKING_DIR}"

# Run the health check and capture exit code
HEALTH_CHECK_EXIT_CODE=0
yarn cy:runLocal --spec "${HEALTH_CHECK_SPEC}" --browser "${BROWSER}" --config chromeWebSecurity=false > "../${HEALTH_CHECK_OUTPUT}" 2>&1 || HEALTH_CHECK_EXIT_CODE=$?

cd ..

echo -e "${CYAN}📄 Health Check Output:${NC}"
echo "----------------------------------------"
tail -20 "${HEALTH_CHECK_OUTPUT}"
echo "----------------------------------------"
echo ""

# Step 2: Check Health Check Results (replicating lines 102-112)
echo -e "${YELLOW}🔍 Step 2: Checking Health Check Results...${NC}"

if [ $HEALTH_CHECK_EXIT_CODE -eq 0 ]; then
    HEALTH_CHECK_OUTCOME="success"
    HEALTH_CHECK_PASSED="true"
    echo -e "${GREEN}✅ Health check passed - proceeding with full test suite${NC}"
else
    HEALTH_CHECK_OUTCOME="failure"
    HEALTH_CHECK_PASSED="false"
    echo -e "${RED}❌ Health check failed - skipping main Cypress tests${NC}"
fi

echo "health_check_passed=${HEALTH_CHECK_PASSED}" >> "${GITHUB_OUTPUT}"
echo ""

# Step 3: Parse Cucumber JSON Report (replicating lines 131-137)
echo -e "${YELLOW}📊 Step 3: Parsing Cucumber JSON Report...${NC}"

if [ -f "${CUCUMBER_REPORT}" ]; then
    echo "✅ Cucumber report found: ${CUCUMBER_REPORT}"
    chmod +x .github/scripts/parse-report.sh
    .github/scripts/parse-report.sh
    echo "✅ Report parsed successfully"
else
    echo "❌ Cucumber report not found: ${CUCUMBER_REPORT}"
    echo "SLACK_MESSAGE<<EOF" >> "${GITHUB_OUTPUT}"
    echo "*Health Check Report Not Found!* Unable to generate a detailed report." >> "${GITHUB_OUTPUT}"
    echo "EOF" >> "${GITHUB_OUTPUT}"
    echo "SLACK_MESSAGE_STATUS=FAILURE" >> "${GITHUB_OUTPUT}"
fi
echo ""

# Step 4: Simulate Slack Notification (replicating lines 138-152)
echo -e "${YELLOW}📱 Step 4: Simulating Slack Notification...${NC}"

# Read the parsed report outputs
if [ -f "${GITHUB_OUTPUT}" ]; then
    SLACK_MESSAGE_STATUS=$(grep "SLACK_MESSAGE_STATUS=" "${GITHUB_OUTPUT}" | cut -d'=' -f2 | tail -1)
    
    # Extract the SLACK_MESSAGE content between EOF markers
    SLACK_MESSAGE=$(sed -n '/SLACK_MESSAGE<<EOF/,/^EOF$/p' "${GITHUB_OUTPUT}" | sed '1d;$d')
else
    SLACK_MESSAGE_STATUS="FAILURE"
    SLACK_MESSAGE="*Health Check Report Not Found!* Unable to generate a detailed report."
fi

# Determine health check status emoji
if [ "${HEALTH_CHECK_OUTCOME}" = "success" ]; then
    HEALTH_CHECK_STATUS_EMOJI="✅ PASSED"
else
    HEALTH_CHECK_STATUS_EMOJI="❌ FAILED"
fi

# Determine if main tests would be skipped
MAIN_TESTS_MESSAGE=""
if [ "${HEALTH_CHECK_PASSED}" = "false" ]; then
    MAIN_TESTS_MESSAGE="*Main tests were skipped due to health check failure*"
fi

# Determine Slack color
if [ "${SLACK_MESSAGE_STATUS}" = "FAILURE" ] || [ "${HEALTH_CHECK_OUTCOME}" = "failure" ]; then
    SLACK_COLOR="danger"
    COLOR_DISPLAY="${RED}🔴 DANGER${NC}"
else
    SLACK_COLOR="good"
    COLOR_DISPLAY="${GREEN}🟢 GOOD${NC}"
fi

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}📱 SIMULATED SLACK NOTIFICATION${NC}"
echo -e "${BLUE}============================================${NC}"
echo ""
echo -e "${CYAN}📋 Slack Notification Metadata:${NC}"
echo "  • Webhook: [SLACK_WEBHOOK from secrets]"
echo "  • Username: Cypress E2E Bot"
echo "  • Channel: team-dev-glc"
echo "  • Color: ${SLACK_COLOR} (${COLOR_DISPLAY})"
echo ""
echo -e "${CYAN}📝 Slack Message Content:${NC}"
echo "----------------------------------------"
echo "${SLACK_MESSAGE}"
echo ""
echo "*Health Check Status:* ${HEALTH_CHECK_STATUS_EMOJI}"
if [ -n "${MAIN_TESTS_MESSAGE}" ]; then
    echo "${MAIN_TESTS_MESSAGE}"
fi
echo "----------------------------------------"
echo ""

# Step 5: Summary
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}📊 TEST SUMMARY${NC}"
echo -e "${BLUE}============================================${NC}"
echo ""
echo -e "${CYAN}🏥 Health Check Results:${NC}"
echo "  • Exit Code: ${HEALTH_CHECK_EXIT_CODE}"
echo "  • Outcome: ${HEALTH_CHECK_OUTCOME}"
echo "  • Status: ${HEALTH_CHECK_STATUS_EMOJI}"
echo "  • Would Proceed to Main Tests: $([ "${HEALTH_CHECK_PASSED}" = "true" ] && echo "✅ YES" || echo "❌ NO")"
echo ""
echo -e "${CYAN}📊 Report Results:${NC}"
echo "  • Cucumber Report Status: ${SLACK_MESSAGE_STATUS}"
echo "  • Slack Notification Color: ${SLACK_COLOR}"
echo ""
echo -e "${CYAN}📁 Generated Files:${NC}"
echo "  • Health Check Output: ${HEALTH_CHECK_OUTPUT}"
echo "  • GitHub Output: ${GITHUB_OUTPUT}"
echo "  • Cucumber Report: ${CUCUMBER_REPORT}"
echo ""

# Final status
if [ "${HEALTH_CHECK_OUTCOME}" = "success" ] && [ "${SLACK_MESSAGE_STATUS}" = "SUCCESS" ]; then
    echo -e "${GREEN}🎉 OVERALL STATUS: HEALTHY - All systems operational!${NC}"
    exit 0
else
    echo -e "${RED}⚠️  OVERALL STATUS: UNHEALTHY - Issues detected!${NC}"
    exit 1
fi
