import './index.scss';
import Dialog from '@components/Dialog';
import Metada<PERSON><PERSON>ield, { ValueType } from '@components/EditMetadata/Field';
import { I18nTranslate } from '@i18n';
import CloseIcon from '@mui/icons-material/Close';
import { Button, IconButton } from '@mui/material';
import { Metadata } from '@shared-types/metadata';
import { useAppDispatch } from '@store/hooks';
import { updateFile } from '@store/modules/caseDetail/slice';
import { selectEvidenceTypeSchema } from '@store/modules/config/slice';
import {
  selectTdoMetadata,
  toggleOpenEditDrawer,
  updateMetadata,
} from '@store/modules/metadata/slice';
import { updateFile as updateFileInSearchResult } from '@store/modules/search/slice';
import { voidWrapper } from '@utils/helpers';
import { useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { capitalize } from 'lodash';

interface Field {
  label: string;
  name: string;
  value?:
    | string
    | number
    | { reason: 'noEngineRun' | 'noResults'; text?: string }
    | { reason?: 'noEngineRun' | 'noResults'; text: string };
  valueType: ValueType;
}

const EditMetadataDrawer = () => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const { data: editMetadata } = useSelector(selectTdoMetadata);
  const evidenceTypeSchemaId = useSelector(selectEvidenceTypeSchema).id;
  const methods = useForm<Metadata>({
    defaultValues: {
      aiwareTdoId: '',
      description: '',
      uploadedDate: '',
      fileSize: 0,
      fileFormat: '',
      duration: 0,
      fileName: '',
      caseId: '',
      caseName: '',
      sourceName: '',
      creator: '',
      evidenceType: '',
    },
  });
  const summaryText =
    editMetadata.summary?.reason === 'noEngineRun'
      ? intl.formatMessage({ id: 'NoEngineRun' })
      : editMetadata.summary?.reason === 'noResults'
        ? intl.formatMessage({ id: 'NoResults' })
        : editMetadata.summary.text;
  const {
    watch,
    reset,
    handleSubmit,
    formState: { isDirty, errors },
  } = methods;
  const metadata = watch();

  useEffect(() => {
    if (editMetadata) {
      reset({
        /* File Metadata */
        aiwareTdoId: editMetadata.aiwareTdoId || '',
        description: editMetadata.description || '',
        summary: { text: summaryText },
        uploadedDate: editMetadata.uploadedDate || '',
        fileSize: editMetadata.fileSize || 0,
        fileFormat: editMetadata.fileFormat || '',
        duration: editMetadata.duration || 0,
        fileName: editMetadata.fileName || '',
        caseId: editMetadata.caseId || '',
        caseName: editMetadata.caseName || '',
        aiCognitionEngineOutput: editMetadata.aiCognitionEngineOutput || '',
        sourceName: editMetadata.sourceName || '',
        creator: editMetadata.creator || '',

        /* Generic Investigate Metadata */
        sourceId: editMetadata.sourceId || undefined,
        contentType: editMetadata.contentType || undefined,
        assetStatus: editMetadata.assetStatus || undefined,

        /* Evidence Types */
        evidenceType: editMetadata.evidenceType || '',
        ...{
          '911 Call Recording': {
            cadId: editMetadata.cadId || '',
            callerPhoneNumber: editMetadata.callerPhoneNumber || '',
          },
          'Arrest Report': {
            reportNumber: editMetadata.reportNumber || '',
          },
          'Body Worn Camera': {
            officerName: editMetadata.officerName || '',
            badgeId: editMetadata.badgeId || '',
            deviceId: editMetadata.deviceId || '',
            cameraPhysicalAddress: editMetadata.cameraPhysicalAddress || '',
            locationTimeline: editMetadata.locationTimeline || [],
          },
          'Booking Photo': {
            firstName: editMetadata.firstName || '',
            lastName: editMetadata.lastName || '',
            dateOfBirth: editMetadata.dateOfBirth || '',
          },
          'Citizen Submitted Video': {
            citizenName: editMetadata.citizenName || '',
            cameraPhysicalAddress: editMetadata.cameraPhysicalAddress || '',
            cameraType: editMetadata.cameraType || undefined,
          },
          'Crime Scene Photo': {
            evidenceTechnician: editMetadata.evidenceTechnician || '',
            badgeId: editMetadata.badgeId || '',
          },
          'In Car Video': {
            officerName: editMetadata.officerName || '',
            unitNumber: editMetadata.unitNumber || '',
            deviceId: editMetadata.deviceId || '',
          },
          'Interview Audio Recording': {
            interviewer: editMetadata.interviewer || '',
            interviewee: editMetadata.interviewee || '',
          },
          'Interview Room Recording': {
            interviewer: editMetadata.interviewer || '',
            interviewee: editMetadata.interviewee || '',
            interviewRoom: editMetadata.interviewRoom || '',
          },
          'Mobile Device Extraction': {
            deviceName: editMetadata.deviceName || '',
            deviceType: editMetadata.deviceType || undefined,
            deviceModel: editMetadata.deviceModel || '',
            deviceRegisteredOwner: editMetadata.deviceRegisteredOwner || '',
          },
          'Security Camera Video': {
            cameraPhysicalAddress: editMetadata.cameraPhysicalAddress || '',
            cameraPhysicalLocation: editMetadata.cameraPhysicalLocation || '',
            cameraFacingDirection: editMetadata.cameraFacingDirection || '',
          },
          '': {},
        }[editMetadata.evidenceType],
      });
    }
  }, [editMetadata]);

  const metadataFields: Field[] = useMemo(
    () => [
      {
        label: intl.formatMessage({ id: 'fileName' }),
        name: 'fileName',
        valueType: ValueType.Input,
      },
      {
        label: intl.formatMessage({ id: 'description' }),
        name: 'description',
        valueType: ValueType.Textarea,
      },
      {
        label: intl.formatMessage({ id: 'summary' }),
        name: 'summary',
        value: metadata.summary,
        valueType: ValueType.Truncate,
      },
      {
        label: intl.formatMessage({ id: 'uploadDate' }),
        name: 'uploadDate',
        value: metadata.uploadedDate,
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'fileSize' }),
        name: 'fileSize',
        value: metadata.fileSize,
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'fileFormat' }),
        name: 'fileFormat',
        value: metadata.fileFormat.toUpperCase(),
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'contentType' }),
        name: 'contentType',
        value: capitalize(metadata.contentType),
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'fileId' }),
        name: 'fileId',
        value: metadata.aiwareTdoId,
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'duration' }),
        name: 'duration',
        value: metadata.duration,
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'createdBy' }),
        name: 'createdBy',
        value: metadata.creator,
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'assetStatus' }),
        name: 'assetStatus',
        value: capitalize(metadata?.assetStatus ?? ''),
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'evidenceType' }),
        name: 'evidenceType',
        valueType: ValueType.Dropdown,
      },
      ...{
        '911 Call Recording': [
          {
            label: intl.formatMessage({ id: 'cadId' }),
            name: 'cadId',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'callerPhoneNumber' }),
            name: 'callerPhoneNumber',
            valueType: ValueType.Input,
          },
        ],
        'Arrest Report': [
          {
            label: intl.formatMessage({ id: 'reportNumber' }),
            name: 'reportNumber',
            valueType: ValueType.Input,
          },
        ],
        'Body Worn Camera': [
          {
            label: intl.formatMessage({ id: 'officerName' }),
            name: 'officerName',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'badgeId' }),
            name: 'badgeId',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'deviceId' }),
            name: 'deviceId',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'cameraPhysicalAddress' }),
            name: 'cameraPhysicalAddress',
            valueType: ValueType.Input, // Support array for when they move
          },
          {
            label: intl.formatMessage({ id: 'locationTimeline' }),
            name: 'locationTimeline',
            valueType: ValueType.Input, // Update to Time-based latitude/longitude coordinates where video was recorded.
          },
        ],
        'Booking Photo': [
          {
            label: intl.formatMessage({ id: 'firstName' }),
            name: 'firstName',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'lastName' }),
            name: 'lastName',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'dateOfBirth' }),
            name: 'dateOfBirth',
            valueType: ValueType.Input,
          },
        ],
        'Citizen Submitted Video': [
          {
            label: intl.formatMessage({ id: 'citizenName' }),
            name: 'citizenName',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'cameraPhysicalAddress' }),
            name: 'cameraPhysicalAddress',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'cameraType' }),
            name: 'cameraType',
            valueType: ValueType.Dropdown,
          },
        ],
        'Crime Scene Photo': [
          {
            label: intl.formatMessage({ id: 'evidenceTechnician' }),
            name: 'evidenceTechnician',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'badgeId' }),
            name: 'badgeId',
            valueType: ValueType.Input,
          },
        ],
        'In Car Video': [
          {
            label: intl.formatMessage({ id: 'officerName' }),
            name: 'officerName',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'unitNumber' }),
            name: 'unitNumber',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'deviceNumber' }),
            name: 'deviceNumber',
            valueType: ValueType.Input,
          },
        ],
        'Interview Audio Recording': [
          {
            label: intl.formatMessage({ id: 'interviewer' }),
            name: 'interviewer',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'interviewee' }),
            name: 'interviewee',
            valueType: ValueType.Input,
          },
        ],
        'Interview Room Recording': [
          {
            label: intl.formatMessage({ id: 'interviewer' }),
            name: 'interviewer',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'interviewee' }),
            name: 'interviewee',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'interviewRoom' }),
            name: 'interviewRoom',
            valueType: ValueType.Input,
          },
        ],
        'Mobile Device Extraction': [
          {
            label: intl.formatMessage({ id: 'deviceName' }),
            name: 'deviceName',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'deviceType' }),
            name: 'deviceType',
            valueType: ValueType.Dropdown,
          },
          {
            label: intl.formatMessage({ id: 'deviceModel' }),
            name: 'deviceModel',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'deviceRegisteredOwner' }),
            name: 'deviceRegisteredOwner',
            valueType: ValueType.Input,
          },
        ],
        'Security Camera Video': [
          {
            label: intl.formatMessage({ id: 'cameraPhysicalAddress' }),
            name: 'cameraPhysicalAddress',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'cameraPhysicalLocation' }),
            name: 'cameraPhysicalLocation',
            valueType: ValueType.Input,
          },
          {
            label: intl.formatMessage({ id: 'cameraFacingDirection' }),
            name: 'cameraFacingDirection',
            valueType: ValueType.Input,
          },
        ],
        '': [],
      }[metadata?.evidenceType ?? ''],
      {
        label: intl.formatMessage({ id: 'caseName' }),
        name: 'caseName',
        value: metadata.caseName,
        valueType: ValueType.ViewOnly,
      },
      {
        label: intl.formatMessage({ id: 'caseId' }),
        name: 'caseId',
        value: metadata.caseId,
        valueType: ValueType.ViewOnly,
      },
      // {
      //   label: intl.formatMessage({ id: 'source' }),
      //   name: 'sourceName',
      //   valueType: ValueType.Dropdown,
      // },
      //   aiCognitionEngineOutput: Array<AICognitionEngineOutput>;
      //   sourceName: string;
      //   sourceId: string;
    ],
    [metadata]
  );

  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const isDisabled = !isDirty || !!Object.keys(errors).length; // TODO improve

  const handleCloseDrawer = () => {
    dispatch(toggleOpenEditDrawer());
  };

  const handleOnSubmit = () => {
    setOpenConfirmDialog(true);
  };

  const handleConfirmSaveChanges = () => {
    dispatch(
      updateMetadata({
        tdoId: editMetadata.aiwareTdoId,
        updatedMetadata: {
          ...metadata,
          veritoneFile: {
            ...editMetadata.veritoneFile,
            fileName: metadata.fileName,
          },
        },
        evidenceTypeSchemaId,
      })
    );
    dispatch(
      updateFile({
        tdoId: editMetadata.aiwareTdoId,
        fileName: metadata.fileName,
        description: metadata.description,
      })
    );
    dispatch(
      updateFileInSearchResult({
        tdoId: editMetadata.aiwareTdoId,
        fileName: metadata.fileName,
      })
    );
    setOpenConfirmDialog(false);
    handleCloseDrawer();
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={voidWrapper(handleSubmit(handleOnSubmit))}
        className="edit-metadata-form"
      >
        <div className="edit-metadata-container">
          <div className="header">
            <p>{intl.formatMessage({ id: 'editMetadata' })}</p>
            <IconButton
              className="close-btn"
              onClick={handleCloseDrawer}
              data-testid="close-btn"
            >
              <CloseIcon />
            </IconButton>
          </div>
          <p className="warning">
            {intl.formatMessage({ id: 'editMetadataWarning' })}
          </p>
          <div className="main-content">
            {metadataFields.map(({ label, name, value, valueType }) => {
              if (
                (valueType === ValueType.ViewOnly ||
                  valueType === ValueType.Truncate) &&
                !value
              ) {
                return null;
              } else {
                return (
                  <MetadataField
                    key={label}
                    label={label}
                    name={name}
                    value={typeof value === 'object' ? value.text : value}
                    valueType={valueType}
                  />
                );
              }
            })}
          </div>
          <div className="footer">
            <Button
              variant="text"
              className="cancel"
              onClick={handleCloseDrawer}
            >
              {intl.formatMessage({ id: 'cancel' })}
            </Button>
            <Button
              variant="contained"
              className="submit"
              disabled={isDisabled}
              type="submit"
            >
              {intl.formatMessage({ id: 'saveChanges' })}
            </Button>
          </div>
        </div>
      </form>
      <Dialog
        open={openConfirmDialog}
        title={intl.formatMessage({ id: 'confirmSaveChanges' })}
        onConfirm={handleConfirmSaveChanges}
        onClose={() => setOpenConfirmDialog(false)}
        confirmText={intl.formatMessage({ id: 'save' })}
        disableConfirm={false}
      >
        {intl.formatMessage({ id: 'wouldYouLikeToSaveYourChanges' })}
      </Dialog>
    </FormProvider>
  );
};

export default EditMetadataDrawer;
