#!/usr/bin/env pwsh
# Script to test health check PASS scenario

Write-Host "============================================" -ForegroundColor Green
Write-Host "HEALTH CHECK TEST - PASS SCENARIO" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green
Write-Host ""

# Ensure we have the original passing cucumber.json
Write-Host "Setting up test environment..." -ForegroundColor Yellow

# Run the parse-report.sh script with Git Bash
Write-Host "Running parse-report.sh script..." -ForegroundColor Yellow

$env:GITHUB_OUTPUT = "output_pass.txt"
$env:GITHUB_RUN_NUMBER = "123"
$env:GITHUB_REF_NAME = "feature/test-branch"
$env:GITHUB_SHA = "abc123def456789"
$env:GITHUB_REPOSITORY = "investigate-app/test-repo"
$env:GITHUB_SERVER_URL = "https://github.com"
$env:GITHUB_RUN_ID = "999999"

# Execute the script using Git Bash
& "C:\Program Files\Git\bin\bash.exe" -c "
export GITHUB_OUTPUT='$env:GITHUB_OUTPUT'
export GITHUB_RUN_NUMBER='$env:GITHUB_RUN_NUMBER'
export GITHUB_REF_NAME='$env:GITHUB_REF_NAME'
export GITHUB_SHA='$env:GITHUB_SHA'
export GITHUB_REPOSITORY='$env:GITHUB_REPOSITORY'
export GITHUB_SERVER_URL='$env:GITHUB_SERVER_URL'
export GITHUB_RUN_ID='$env:GITHUB_RUN_ID'

bash .github/scripts/parse-report.sh
"

Write-Host ""
Write-Host "============================================" -ForegroundColor Green
Write-Host "RESULTS:" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

# Check the status
$status = Get-Content "output_pass.txt" | Select-String "SLACK_MESSAGE_STATUS" | Select-Object -Last 1
Write-Host "Status: $status" -ForegroundColor $(if ($status -match "SUCCESS") { "Green" } else { "Red" })

Write-Host ""
Write-Host "Full Output:" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan
Get-Content "output_pass.txt"

Write-Host ""
Write-Host "Test completed! Check output_pass.txt for full details." -ForegroundColor Green
