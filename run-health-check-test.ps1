#!/usr/bin/env pwsh
# PowerShell wrapper for the standalone health check test script

param(
    [string]$GitBashPath = "C:\Program Files\Git\bin\bash.exe"
)

Write-Host "============================================" -ForegroundColor Blue
Write-Host "🏥 HEALTH CHECK TEST RUNNER" -ForegroundColor Blue
Write-Host "============================================" -ForegroundColor Blue
Write-Host ""

# Check if Git Bash exists
if (-not (Test-Path $GitBashPath)) {
    Write-Host "❌ Git Bash not found at: $GitBashPath" -ForegroundColor Red
    Write-Host "Please install Git for Windows or specify the correct path with -GitBashPath parameter" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Found Git Bash at: $GitBashPath" -ForegroundColor Green
Write-Host ""

# Check if the bash script exists
$bashScript = "test-health-check-standalone.sh"
if (-not (Test-Path $bashScript)) {
    Write-Host "❌ Health check script not found: $bashScript" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found health check script: $bashScript" -ForegroundColor Green
Write-Host ""

# Set environment variables if not already set
if (-not $env:GITHUB_RUN_NUMBER) { $env:GITHUB_RUN_NUMBER = "999" }
if (-not $env:GITHUB_REF_NAME) { $env:GITHUB_REF_NAME = "feature/health-check-test" }
if (-not $env:GITHUB_REPOSITORY) { $env:GITHUB_REPOSITORY = "investigate-app/test-repo" }
if (-not $env:GITHUB_SERVER_URL) { $env:GITHUB_SERVER_URL = "https://github.com" }
if (-not $env:GITHUB_RUN_ID) { $env:GITHUB_RUN_ID = "888888" }

# Get current commit SHA if in a git repository
try {
    $gitSha = git rev-parse HEAD 2>$null
    if ($gitSha) {
        $env:GITHUB_SHA = $gitSha
    } else {
        $env:GITHUB_SHA = "abc123def456789"
    }
} catch {
    $env:GITHUB_SHA = "abc123def456789"
}

Write-Host "🔧 Environment Configuration:" -ForegroundColor Cyan
Write-Host "  • GITHUB_RUN_NUMBER: $env:GITHUB_RUN_NUMBER"
Write-Host "  • GITHUB_REF_NAME: $env:GITHUB_REF_NAME"
Write-Host "  • GITHUB_SHA: $($env:GITHUB_SHA.Substring(0, [Math]::Min(7, $env:GITHUB_SHA.Length)))"
Write-Host "  • GITHUB_REPOSITORY: $env:GITHUB_REPOSITORY"
Write-Host "  • GITHUB_SERVER_URL: $env:GITHUB_SERVER_URL"
Write-Host "  • GITHUB_RUN_ID: $env:GITHUB_RUN_ID"
Write-Host ""

Write-Host "🚀 Starting health check test..." -ForegroundColor Yellow
Write-Host ""

# Run the bash script
try {
    & $GitBashPath -c "./test-health-check-standalone.sh"
    $exitCode = $LASTEXITCODE
} catch {
    Write-Host "❌ Error running health check script: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "============================================" -ForegroundColor Blue
Write-Host "🏁 TEST COMPLETED" -ForegroundColor Blue
Write-Host "============================================" -ForegroundColor Blue

if ($exitCode -eq 0) {
    Write-Host "✅ Health check test completed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Health check test completed with issues!" -ForegroundColor Red
}

Write-Host ""
Write-Host "📁 Check the generated files for detailed results:" -ForegroundColor Cyan
Get-ChildItem -Filter "*health_check_output_*.txt" | ForEach-Object {
    Write-Host "  • $($_.Name)" -ForegroundColor White
}
Get-ChildItem -Filter "*github_output_*.txt" | ForEach-Object {
    Write-Host "  • $($_.Name)" -ForegroundColor White
}

exit $exitCode
