[{"description": "", "elements": [{"description": "", "id": "search-results;verify-user-can-edit-a-file-by-meta-data", "keyword": "<PERSON><PERSON><PERSON>", "line": 140, "name": "Verify user can edit a file by meta data", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "The user is on the Search screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:17"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "The user changes group view to 'Un-Grouped'", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:99"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Given ", "line": 7, "name": "The user is on the Search screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:17"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 141, "name": "The user closes the \"Advanced Search\" section", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:50"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 142, "name": "The user opens the \"Case Information\" section", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:50"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 143, "name": "The user selects a date range from day 01 to day 02 in 'June'", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:47"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 144, "name": "The user clicks the search button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:135"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 145, "name": "The user sees case name \"lucy.mp4\" in search results", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:29"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@e2e", "line": 139}, {"name": "@search", "line": 139}, {"name": "@failed", "line": 139}, {"name": "@skip", "line": 139}], "type": "scenario"}, {"description": "", "id": "search-results;verify-user-can-delete-a-file-through-kebab-menu", "keyword": "<PERSON><PERSON><PERSON>", "line": 196, "name": "Verify user can delete a file through kebab menu", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "The user is on the Search screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:17"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "The user changes group view to 'Un-Grouped'", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:99"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Given ", "line": 7, "name": "The user is on the Search screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:17"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 197, "name": "The user closes the \"Advanced Search\" section", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:50"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 198, "name": "The user opens the \"Case Information\" section", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:50"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 199, "name": "The user selects a date range from day 01 to day 02 in 'June'", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:47"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 200, "name": "The user sees case name \"lucy.mp4\" in search results", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:29"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@e2e", "line": 195}, {"name": "@search", "line": 195}, {"name": "@failed", "line": 195}, {"name": "@skip", "line": 195}], "type": "scenario"}, {"description": "", "id": "search-results;verify-user-can-delete-multiple-files-through", "keyword": "<PERSON><PERSON><PERSON>", "line": 205, "name": "Verify user can delete multiple files through", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@e2e", "line": 204}, {"name": "@search", "line": 204}, {"name": "@failed", "line": 204}, {"name": "@skip", "line": 204}], "type": "scenario"}, {"description": "", "id": "search-results;verify-user-can-sort-search-table-columns", "keyword": "<PERSON><PERSON><PERSON>", "line": 211, "name": "Verify user can sort search table columns", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 2018000000}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "The user is on the Search screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:17"}, "result": {"status": "passed", "duration": 16447000000}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "The user changes group view to 'Un-Grouped'", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:99"}, "result": {"status": "passed", "duration": 404000000}}, {"arguments": [], "keyword": "Given ", "line": 7, "name": "The user is on the Search screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:17"}, "result": {"status": "passed", "duration": 12498000000}}, {"arguments": [], "keyword": "When ", "line": 212, "name": "The user enters keyword \"lucy.mp4\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:26"}, "result": {"status": "passed", "duration": 974000000}}, {"arguments": [], "keyword": "And ", "line": 213, "name": "The user clicks the search button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:135"}, "result": {"status": "passed", "duration": 341000000}}, {"arguments": [], "keyword": "Then ", "line": 214, "name": "The user sees case name \"lucy.mp4\" in search results", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:29"}, "result": {"status": "passed", "duration": 13261000000}}, {"arguments": [{"rows": [{"cells": ["column", "expectedDirection"]}, {"cells": ["File Name", "descending"]}, {"cells": ["File Name", "ascending"]}, {"cells": ["Date Uploaded", "ascending"]}, {"cells": ["Date Uploaded", "descending"]}]}], "keyword": "When ", "line": 215, "name": "The user performs the following sorting operations:", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/search-results/search-results.steps.ts:171"}, "result": {"status": "passed", "duration": 49859000000}}], "tags": [{"name": "@e2e", "line": 210}, {"name": "@search", "line": 210}], "type": "scenario"}], "id": "search-results", "line": 2, "keyword": "Feature", "name": "Search Results", "tags": [], "uri": "cypress\\e2e\\features\\search-results.feature"}]