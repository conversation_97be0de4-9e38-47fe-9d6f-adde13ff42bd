SLACK_MESSAGE<<EOF
*Cypress Cucumber Test Report - Build 456*
*Status:* FAILURE
*Branch:* `feature/test-branch`
*Latest Commit:* `def789a`
*Workflow Link:* <https://github.com/investigate-app/test-repo/actions/runs/888888|View Workflow>

*Summary Per Feature:*
```
+--------------------------------------------+
| Feature        Total Passed Failed Skipped |
+--------------------------------------------+
| search-results 4     -      1      3       |
| Total          4     -      2      3       |
+--------------------------------------------+
```

*Failed Scenarios Details:*
- *Feature:* `search-results`
  *Scenario:* `Verify user can sort search table columns`
- *Feature:* `search-results`
  *Scenario:* `Verify user can sort search table columns`
EOF
SLACK_MESSAGE_STATUS=FAILURE
